#pragma once

#include <opencv2/opencv.hpp>
#include <memory>
#include "SharedData.hpp"

/**
 * @brief 帧渲染工具类，用于在视频帧上绘制YOLO检测框和标签
 */
class FrameRenderer {
public:
    /**
     * @brief 构造函数
     * @param shared_data 共享数据指针，用于获取检测结果
     */
    FrameRenderer(std::shared_ptr<SharedData> shared_data);

    /**
     * @brief 在帧上渲染YOLO检测框和标签
     * @param frame 输入帧（会被修改）
     * @param camera_id 摄像头ID
     * @param enable_detection_boxes 是否启用检测框绘制
     * @return 渲染后的帧
     */
    cv::Mat renderDetections(const cv::Mat& frame, int camera_id, bool enable_detection_boxes = true);

    /**
     * @brief 在帧上渲染YOLO检测框和标签（静态版本）
     * @param frame 输入帧
     * @param camera_id 摄像头ID 
     * @param shared_data 共享数据指针
     * @param enable_detection_boxes 是否启用检测框绘制
     * @return 渲染后的帧
     */
    static cv::Mat renderDetectionsStatic(const cv::Mat& frame, int camera_id, 
                                         std::shared_ptr<SharedData> shared_data,
                                         bool enable_detection_boxes = true);

    /**
     * @brief 设置检测框颜色
     * @param color BGR颜色值
     */
    void setBoxColor(const cv::Scalar& color);

    /**
     * @brief 设置文本颜色
     * @param color BGR颜色值
     */
    void setTextColor(const cv::Scalar& color);

    /**
     * @brief 设置检测框线宽
     * @param thickness 线宽
     */
    void setBoxThickness(int thickness);

private:
    std::shared_ptr<SharedData> shared_data_;
    cv::Scalar box_color_;      // 检测框颜色
    cv::Scalar text_color_;     // 文本颜色
    int box_thickness_;         // 检测框线宽
    double font_scale_;         // 字体缩放
    int font_face_;             // 字体类型

    /**
     * @brief 内部渲染函数
     */
    cv::Mat renderDetectionsInternal(const cv::Mat& frame, int camera_id, bool enable_detection_boxes);
};