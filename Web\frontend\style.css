/* General Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Orbitron', 'Segoe UI', 'Roboto', sans-serif;
}

:root {
    --primary-color: #00d4ff;
    --secondary-color: #00ffaa;
    --accent-color: #ff6b35;
    --background-color: #0a0a0f;
    --surface-color: #1a1a2e;
    --surface-hover: #252545;
    --border-color: rgba(0, 212, 255, 0.3);
    --border-glow: rgba(0, 212, 255, 0.6);
    --text-color: #ffffff;
    --text-muted-color: #a0a0a0;
    --success-color: #00ffaa;
    --warning-color: #ffaa00;
    --error-color: #ff4466;
    --record-all-color: #ff8c00;
    --glow-shadow: 0 0 8px var(--primary-color), 0 0 16px var(--primary-color), 0 0 24px var(--secondary-color);
    --subtle-glow: 0 0 4px var(--primary-color);
    --transition-speed: 0.3s;
    --border-radius: 8px;
    --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

body {
    background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 100%);
    color: var(--text-color);
    overflow-x: hidden;
    background-attachment: fixed;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(0, 255, 170, 0.08) 0%, transparent 50%),
        linear-gradient(rgba(0, 212, 255, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 212, 255, 0.03) 1px, transparent 1px);
    background-size: 100% 100%, 100% 100%, 30px 30px, 30px 30px;
    pointer-events: none;
    z-index: -1;
}

.container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 24px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    gap: 24px;
}

/* Header */
.header {
    background: linear-gradient(135deg, var(--surface-color) 0%, rgba(26, 26, 46, 0.8) 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px 24px;
    box-shadow: var(--card-shadow);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--primary-color));
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo h1 {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--primary-color);
    text-shadow: var(--glow-shadow);
    letter-spacing: 1px;
}

.logo i {
    font-size: 2.4rem;
    color: var(--secondary-color);
    filter: drop-shadow(0 0 8px var(--secondary-color));
}

.status-indicator, .fps-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    font-size: 0.95rem;
    font-weight: 500;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--error-color);
    box-shadow: 0 0 12px var(--error-color);
    animation: pulse-red 2s infinite;
}

.status-dot.online {
    background-color: var(--success-color);
    box-shadow: 0 0 12px var(--success-color);
    animation: pulse-green 2s infinite;
}

@keyframes pulse-red { 0%, 100% { box-shadow: 0 0 4px var(--error-color); } 50% { box-shadow: 0 0 12px var(--error-color); } }
@keyframes pulse-green { 0%, 100% { box-shadow: 0 0 4px var(--success-color); } 50% { box-shadow: 0 0 12px var(--success-color); } }
@keyframes pulse-orange { 
    0%, 100% { box-shadow: 0 0 4px var(--record-all-color); } 
    50% { box-shadow: 0 0 12px var(--record-all-color); } 
}

@keyframes pulse-rec {
  0% {
    box-shadow: 0 0 4px var(--error-color), 0 0 6px var(--error-color);
  }
  50% {
    box-shadow: 0 0 12px var(--error-color), 0 0 20px var(--error-color);
  }
  100% {
    box-shadow: 0 0 4px var(--error-color), 0 0 6px var(--error-color);
  }
}

/* Main Content & Sections */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
}

section {
    background: linear-gradient(135deg, var(--surface-color) 0%, rgba(26, 26, 46, 0.9) 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 24px;
    position: relative;
    transition: all var(--transition-speed);
    box-shadow: var(--card-shadow);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

section:hover {
    border-color: var(--border-glow);
    box-shadow: 0 8px 32px rgba(0, 212, 255, 0.15);
    transform: translateY(-2px);
}

section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    opacity: 0.6;
}

section h2 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.4rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
    text-shadow: var(--subtle-glow);
}

section h2 i {
    color: var(--secondary-color);
    filter: drop-shadow(0 0 4px var(--secondary-color));
}

/* Dashboard Metrics */
.dashboard-metrics {
    margin-bottom: 0;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.metric-card {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(0, 255, 170, 0.05) 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: all var(--transition-speed);
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.metric-card:hover {
    border-color: var(--border-glow);
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.15) 0%, rgba(0, 255, 170, 0.1) 100%);
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 212, 255, 0.2);
}

.metric-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    box-shadow: var(--subtle-glow);
}

.metric-icon i {
    font-size: 1.8rem;
    color: var(--background-color);
}

.metric-content {
    flex: 1;
}

.metric-content .metric-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-color);
    text-shadow: var(--subtle-glow);
    line-height: 1.2;
}

.metric-content .metric-value .unit {
    font-size: 1rem;
    font-weight: 400;
    color: var(--text-muted-color);
    margin-left: 4px;
}

.metric-content .metric-label {
    font-size: 0.9rem;
    color: var(--text-muted-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 4px;
}

/* Control Dashboard */
.control-dashboard {
    padding: 0;
    background: none;
    border: none;
    box-shadow: none;
}

.control-dashboard::before {
    display: none;
}

.global-controls {
    display: grid;
    grid-template-columns: 1.2fr 1fr;
    gap: 20px;
}

.global-controls > div {
    background: linear-gradient(135deg, var(--surface-color) 0%, rgba(26, 26, 46, 0.9) 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 24px;
    position: relative;
    transition: all var(--transition-speed);
    box-shadow: var(--card-shadow);
    backdrop-filter: blur(10px);
}

.global-controls > div:hover {
    border-color: var(--border-glow);
    box-shadow: 0 8px 32px rgba(0, 212, 255, 0.15);
    transform: translateY(-2px);
}

.global-controls > div::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    opacity: 0.6;
}

.global-controls h2 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    text-shadow: var(--subtle-glow);
}

/* Analysis Section */
.analysis-section {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.analysis-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.analysis-grid > section {
    margin-bottom: 0;
}

/* Control Panel Styles */
.control-panel .controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-bottom: 20px;
}

.threshold-control {
    grid-column: 1 / -1;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    margin-top: 16px;
}

.threshold-control label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    color: var(--text-color);
    font-size: 0.95rem;
    font-weight: 500;
}

.slider-container {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* Status Panel */
.status-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.status-card {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all var(--transition-speed);
}

.status-card:hover {
    border-color: var(--border-glow);
    background: rgba(0, 212, 255, 0.05);
}

.status-icon i {
    font-size: 1.4rem;
    color: var(--primary-color);
}

.status-info {
    flex: 1;
}

.status-title {
    font-size: 0.8rem;
    color: var(--text-muted-color);
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-value {
    font-weight: 600;
    font-size: 0.95rem;
    color: var(--text-color);
}

/* Trajectory Viewer */
.trajectory-viewer {
    min-height: 500px;
}

.trajectory-canvas-container {
    width: 100%;
    height: 400px; /* Or a preferred height */
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    position: relative;
    border: 1px dashed var(--border-color);
}

.trajectory-canvas-container canvas {
    display: block; /* Remove any default spacing */
}

/* Key Metrics Section */
.key-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    padding: 0;
}

.key-metrics .summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 20px 16px;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: all var(--transition-speed);
    position: relative;
    overflow: hidden;
}

.key-metrics .summary-item:hover {
    border-color: var(--border-glow);
    background: rgba(0, 212, 255, 0.1);
    transform: translateY(-2px);
}

.key-metrics .summary-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0.8;
}

.key-metrics .summary-item i {
    font-size: 2.4rem;
    color: var(--secondary-color);
    filter: drop-shadow(0 0 8px var(--secondary-color));
    margin-bottom: 4px;
}

.key-metrics .metric-value {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
    color: var(--text-color);
    text-shadow: var(--subtle-glow);
}

.key-metrics .metric-value .unit {
    font-size: 0.9rem;
    font-weight: 400;
    color: var(--text-muted-color);
    margin-left: 4px;
}

.key-metrics .metric-label {
    font-size: 0.8rem;
    text-transform: uppercase;
    color: var(--text-muted-color);
    letter-spacing: 0.5px;
    font-weight: 500;
}



/* Control Panel */
.control-panel {
    position: relative;
}
.control-panel .controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-bottom: 20px;
}


.control-panel .threshold-control {
    grid-column: 1 / -1;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    margin-top: 16px;
}

.control-panel .threshold-control label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    color: var(--text-color);
    font-size: 0.95rem;
    font-weight: 500;
}

.btn {
    background: linear-gradient(135deg, transparent 0%, rgba(0, 212, 255, 0.1) 100%);
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    padding: 12px 20px;
    cursor: pointer;
    transition: all var(--transition-speed);
    text-transform: uppercase;
    font-weight: 600;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover:not(:disabled)::before {
    left: 100%;
}

.btn:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-color) 0%, rgba(0, 255, 170, 0.8) 100%);
    color: var(--background-color);
    box-shadow: var(--glow-shadow);
    transform: translateY(-2px);
}

.btn:disabled {
    background: rgba(51, 51, 51, 0.5);
    color: #666;
    cursor: not-allowed;
    box-shadow: none;
    border-color: #444;
}

.btn.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--background-color);
    box-shadow: var(--subtle-glow);
}

.btn.btn-record-all.recording-all {
    background: linear-gradient(135deg, var(--record-all-color) 0%, #ff6b35 100%);
    color: var(--background-color);
    box-shadow: 0 0 16px var(--record-all-color);
    animation: pulse-orange 2s infinite;
}

/* 精彩录制控件样式 */
.highlight-controls {
    margin-top: 30px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.05) 0%, rgba(255, 140, 0, 0.02) 100%);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
}

.highlight-controls::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, #ffd700, transparent);
    opacity: 0.6;
}

.highlight-controls h3 {
    color: #ffd700;
    margin-bottom: 15px;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.3);
}

.highlight-controls h3 i {
    color: #ff8c00;
    filter: drop-shadow(0 0 4px #ff8c00);
}

.btn-highlight-start {
    background: linear-gradient(135deg, #ffd700 0%, #ff8c00 100%);
    border: 1px solid #ffd700;
    color: var(--background-color);
    box-shadow: 0 0 8px rgba(255, 215, 0, 0.4);
}

.btn-highlight-start:hover:not(:disabled) {
    background: linear-gradient(135deg, #ffed4e 0%, #ffb347 100%);
    box-shadow: 0 0 16px rgba(255, 215, 0, 0.6);
    transform: translateY(-2px);
}

.btn-highlight-stop {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    border: 1px solid #ff6b35;
    color: var(--background-color);
    box-shadow: 0 0 8px rgba(255, 107, 53, 0.4);
    animation: pulse-highlight 2s infinite;
}

.btn-highlight-stop:hover:not(:disabled) {
    background: linear-gradient(135deg, #ff8c5a 0%, #ffb347 100%);
    box-shadow: 0 0 16px rgba(255, 107, 53, 0.6);
    transform: translateY(-2px);
}

@keyframes pulse-highlight {
    0%, 100% {
        box-shadow: 0 0 8px rgba(255, 107, 53, 0.4);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 107, 53, 0.8);
    }
}

.highlight-status {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 15px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.highlight-status .status-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.highlight-status .label {
    font-size: 0.85rem;
    color: var(--text-muted-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.highlight-status .value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffd700;
    text-shadow: 0 0 4px rgba(255, 215, 0, 0.3);
}

.highlight-settings {
    margin-top: 15px;
}

.highlight-settings .setting-item {
    margin-bottom: 15px;
}

.highlight-settings label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    color: var(--text-color);
    font-weight: 500;
}

.highlight-settings label i {
    color: #ffd700;
}

/* Slider Styles */
input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 6px;
    background: linear-gradient(to right, var(--border-color) 0%, var(--primary-color) 50%, var(--border-color) 100%);
    outline: none;
    border-radius: 3px;
    position: relative;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    cursor: pointer;
    border: 3px solid var(--background-color);
    box-shadow: 0 0 12px var(--primary-color);
    border-radius: 50%;
    transition: all var(--transition-speed);
}

input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 0 20px var(--primary-color);
}

input[type="range"]::-moz-range-thumb {
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    cursor: pointer;
    border: 3px solid var(--background-color);
    box-shadow: 0 0 12px var(--primary-color);
    border-radius: 50%;
    transition: all var(--transition-speed);
}

#thresholdValue {
    color: var(--secondary-color);
    font-weight: 700;
    text-align: center;
    min-width: 50px;
    padding: 4px 8px;
    background: rgba(0, 255, 170, 0.1);
    border-radius: var(--border-radius);
    border: 1px solid var(--secondary-color);
}

/* ==================== 检测框录制控制样式 ==================== */

.detection-recording-controls {
    margin-top: 30px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(0, 255, 170, 0.05) 0%, rgba(0, 212, 255, 0.02) 100%);
    border: 1px solid rgba(0, 255, 170, 0.2);
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
}

.detection-recording-controls::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, #00ffaa, transparent);
    opacity: 0.6;
}

.detection-recording-controls h3 {
    color: #00ffaa;
    margin-bottom: 15px;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    text-shadow: 0 0 8px rgba(0, 255, 170, 0.3);
}

.detection-recording-controls h3 i {
    color: #00d4ff;
    filter: drop-shadow(0 0 4px #00d4ff);
}

.btn-detection-enable {
    background: linear-gradient(135deg, #00ffaa 0%, #00d4ff 100%);
    border: 1px solid #00ffaa;
    color: var(--background-color);
    box-shadow: 0 0 8px rgba(0, 255, 170, 0.4);
}

.btn-detection-enable:hover:not(:disabled) {
    background: linear-gradient(135deg, #4fffcc 0%, #4de5ff 100%);
    box-shadow: 0 0 16px rgba(0, 255, 170, 0.6);
    transform: translateY(-2px);
}

.btn-detection-disable {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    border: 1px solid #ff6b35;
    color: var(--background-color);
    box-shadow: 0 0 8px rgba(255, 107, 53, 0.4);
}

.btn-detection-disable:hover:not(:disabled) {
    background: linear-gradient(135deg, #ff8c5a 0%, #ffb347 100%);
    box-shadow: 0 0 16px rgba(255, 107, 53, 0.6);
    transform: translateY(-2px);
}

.detection-status {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 15px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius);
    border: 1px solid rgba(0, 255, 170, 0.1);
}

.detection-status .status-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.detection-status .label {
    font-size: 0.85rem;
    color: var(--text-muted-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detection-status .value {
    font-size: 1rem;
    font-weight: 600;
    color: #00ffaa;
    text-shadow: 0 0 4px rgba(0, 255, 170, 0.3);
}

.camera-detection-controls {
    margin-top: 15px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.camera-control {
    padding: 12px;
    background: rgba(0, 255, 170, 0.05);
    border: 1px solid rgba(0, 255, 170, 0.1);
    border-radius: var(--border-radius);
    transition: all var(--transition-speed);
}

.camera-control:hover {
    background: rgba(0, 255, 170, 0.08);
    border-color: rgba(0, 255, 170, 0.2);
}

.camera-control label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-color);
    user-select: none;
}

.camera-control input[type="checkbox"] {
    position: relative;
    width: 20px;
    height: 20px;
    appearance: none;
    background: transparent;
    border: 2px solid #00ffaa;
    border-radius: 4px;
    cursor: pointer;
    transition: all var(--transition-speed);
}

.camera-control input[type="checkbox"]:checked {
    background: linear-gradient(135deg, #00ffaa, #00d4ff);
    border-color: #00ffaa;
    box-shadow: 0 0 8px rgba(0, 255, 170, 0.4);
}

.camera-control input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--background-color);
    font-weight: bold;
    font-size: 0.9rem;
}

.camera-control input[type="checkbox"]:hover {
    border-color: #4fffcc;
    box-shadow: 0 0 6px rgba(0, 255, 170, 0.3);
}

/* Camera Display */
.camera-display {
    padding: 0;
    background: none;
    border: none;
    box-shadow: none;
}

.camera-display::before {
    display: none;
}

.camera-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.camera-item {
    border: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--surface-color) 0%, rgba(26, 26, 46, 0.9) 100%);
    border-radius: var(--border-radius);
    display: flex;
    flex-direction: column;
    transition: all var(--transition-speed);
    box-shadow: var(--card-shadow);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.camera-item:hover {
    border-color: var(--border-glow);
    box-shadow: 0 8px 32px rgba(0, 212, 255, 0.15);
    transform: translateY(-2px);
}

.camera-item.is-recording {
    border-color: var(--error-color);
    box-shadow: 0 0 20px rgba(255, 68, 102, 0.4), 0 0 40px rgba(255, 68, 102, 0.2);
    animation: recording-pulse 2s infinite;
}

@keyframes recording-pulse {
    0%, 100% {
        box-shadow: 0 0 20px rgba(255, 68, 102, 0.4), 0 0 40px rgba(255, 68, 102, 0.2);
    }
    50% {
        box-shadow: 0 0 30px rgba(255, 68, 102, 0.6), 0 0 60px rgba(255, 68, 102, 0.3);
    }
}

.camera-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.2);
}

.camera-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.camera-view {
    position: relative;
    background: linear-gradient(135deg, #000 0%, #111 100%);
    aspect-ratio: 16 / 9;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.video-canvas {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 4px;
}

.trajectory-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.no-signal {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #000;
    color: var(--text-muted-color);
    z-index: 10;
}
.no-signal i { font-size: 3rem; margin-bottom: 1rem; color: #ff4444; }

.camera-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: rgba(10, 10, 15, 0.7);
    margin-top: 0.5rem;
    border-top: 1px solid var(--border-color);
}

.rec-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: var(--text-muted-color);
}

.rec-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #555;
    transition: all var(--transition-speed);
}

.is-recording .rec-status span {
    color: var(--error-color);
    font-weight: bold;
}

.is-recording .rec-dot {
    background-color: var(--error-color);
    animation: pulse-rec 1.5s infinite;
    transform: scale(1.2);
}

/* Coordinates & DB Panels */
.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}
.panel-header h2 {
    margin-bottom: 0;
}

.coordinates-display {
    display: flex;
    flex-direction: column;
}

.coordinates-panel {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    border: none;
    padding: 0;
}
.coordinates-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1rem;
}
.coordinate-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
.coordinate-controls { display: none; } /* Hide old controls */

.coordinates-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    flex-grow: 1;
}
.coordinates-summary {
    display: none; /* We moved this to key-metrics, hide the old one if it exists */
}

.coordinates-table-container {
    flex-grow: 1;
    overflow-y: auto;
    max-height: 300px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: linear-gradient(135deg, #000 0%, #111 100%);
}

.coordinates-table {
    width: 100%;
    border-collapse: collapse;
}

.coordinates-table th, .coordinates-table td {
    padding: 12px 16px;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.coordinates-table th {
    background: linear-gradient(135deg, var(--surface-color) 0%, rgba(26, 26, 46, 0.9) 100%);
    color: var(--primary-color);
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: sticky;
    top: 0;
    z-index: 1;
}

.coordinates-table th i {
    margin-right: 6px;
    color: var(--secondary-color);
}

.coordinates-table td {
    font-family: 'JetBrains Mono', 'Roboto Mono', monospace;
    font-size: 0.9rem;
    color: var(--text-color);
}

.coordinate-row:hover {
    background-color: rgba(0, 212, 255, 0.1);
}

.no-data-row td {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-muted-color);
    font-style: italic;
}

.no-data-row i {
    margin-right: 8px;
    color: var(--primary-color);
    opacity: 0.6;
}

/* System Status Refactor */
.status-panel .status-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}
.status-card {
    background-color: rgba(0,0,0,0.2);
    border: 1px solid var(--border-color);
    padding: 0.75rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all var(--transition-speed);
}
.status-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
}
.status-info {
    flex-grow: 1;
}
.status-title {
    font-size: 0.8rem;
    color: var(--text-muted-color);
    margin-bottom: 0.25rem;
}
.status-value {
    font-weight: 700;
    font-size: 1rem;
}

/* DB Explorer */
.db-explorer {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.db-explorer-panel {
    display: flex;
    flex-direction: column;
    gap: 16px;
    flex-grow: 1;
}

.query-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.sql-input {
    background: linear-gradient(135deg, #000 0%, #111 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-color);
    padding: 16px;
    font-family: 'JetBrains Mono', 'Roboto Mono', monospace;
    font-size: 0.9rem;
    resize: vertical;
    min-height: 80px;
    transition: all var(--transition-speed);
}

.sql-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--subtle-glow);
    background: linear-gradient(135deg, #000 0%, #1a1a2e 100%);
}

.sql-input::placeholder {
    color: var(--text-muted-color);
    font-style: italic;
}

.status-message {
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.9rem;
    display: none; /* Show with JS */
}
.status-message.error {
    background-color: rgba(255, 68, 68, 0.2);
    color: var(--error-color);
    display: block;
}
.status-message.success {
    background-color: rgba(0, 255, 170, 0.2);
    color: var(--success-color);
    display: block;
}
.results-container {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: linear-gradient(135deg, #000 0%, #111 100%);
    padding: 16px;
    overflow-y: auto;
    min-height: 200px;
    max-height: 400px;
    flex-grow: 1;
}

.results-container .no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-muted-color);
    text-align: center;
    padding: 40px 20px;
    height: 100%;
}

.results-container .no-results i {
    font-size: 2rem;
    margin-bottom: 12px;
    color: var(--primary-color);
    opacity: 0.6;
}

.results-container .no-results p {
    font-size: 0.95rem;
    margin: 0;
}
.db-results-table {
    width: 100%;
    border-collapse: collapse;
}
.db-results-table th, 
.db-results-table td {
    border: 1px solid var(--border-color);
    padding: 8px 12px;
    text-align: left;
    font-size: 0.9rem;
}
.db-results-table th {
    background-color: var(--surface-color);
    color: var(--secondary-color);
    position: sticky;
    top: 0;
    z-index: 1;
}
.db-results-table tr:nth-child(even) {
    background-color: rgba(0, 170, 255, 0.05);
}
.db-results-table tr:hover {
    background-color: rgba(0, 170, 255, 0.15);
}

/* Scrollbar styling for results container */
.results-container::-webkit-scrollbar {
    width: 8px;
}
.results-container::-webkit-scrollbar-track {
    background: var(--background-color);
}
.results-container::-webkit-scrollbar-thumb {
    background-color: var(--primary-color);
    border: 1px solid var(--background-color);
}
.results-container::-webkit-scrollbar-thumb:hover {
    background-color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 1400px) {
    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 1200px) {
    .analysis-grid {
        grid-template-columns: 1fr;
    }

    .global-controls {
        grid-template-columns: 1fr;
    }

    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .visualization-grid {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: auto auto auto;
    }
    
    .featured-chart {
        grid-column: 1 / -1;
        grid-row: 1;
    }
}

@media (max-width: 992px) {
    .camera-grid {
        grid-template-columns: 1fr;
    }

    .status-grid {
        grid-template-columns: 1fr;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
    }

    .visualization-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto auto;
    }
    
    .featured-chart {
        grid-column: 1;
        grid-row: 1;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 16px;
        gap: 16px;
    }

    .header-content {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }

    .logo h1 {
        font-size: 1.8rem;
    }

    .control-panel .controls {
        grid-template-columns: 1fr;
    }

    .metric-card {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .metric-icon {
        width: 50px;
        height: 50px;
    }

    .metric-icon i {
        font-size: 1.5rem;
    }

    section {
        padding: 16px;
    }

    .global-controls > div {
        padding: 16px;
    }

    .footer-content {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }

    .footer-info {
        text-align: center;
    }

    .footer-info p {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 16px;
    }

    .logo h1 {
        font-size: 1.5rem;
    }

    .metric-content .metric-value {
        font-size: 1.5rem;
    }

    .btn {
        padding: 10px 16px;
        font-size: 0.85rem;
    }
}

/* Notification System */
#notification-container {
    position: fixed;
    top: 80px; /* Position below header */
    right: 20px;
    z-index: 2000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.notification {
    background-color: var(--surface-color);
    color: var(--text-color);
    padding: 15px 20px;
    border-radius: 5px;
    border-left: 5px solid var(--primary-color);
    box-shadow: 0 2px 10px rgba(0,0,0,0.5);
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55);
    width: 300px;
}

.notification.show {
    opacity: 1;
    transform: translateX(0);
}

.notification.success { border-left-color: var(--success-color); }
.notification.error { border-left-color: var(--error-color); }
.notification.info { border-left-color: var(--primary-color); }

/* Make "Data Time" smaller */
#metric-last-update i {
    font-size: 1.5rem;
}
#metric-last-update .metric-value {
    font-size: 1.5rem;
}

/* Modal (Enlarged View) */
.modal {
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.85);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    max-width: 90%;
    max-height: 90%;
    width: auto;
    height: auto;
    object-fit: contain;
    border: 3px solid var(--secondary-color);
    box-shadow: 0 0 30px var(--secondary-color);
}

.close-btn {
    position: absolute;
    top: 20px;
    right: 35px;
    color: #fff;
    font-size: 50px;
    font-weight: bold;
    transition: 0.3s;
    cursor: pointer;
    z-index: 1010;
}

.close-btn:hover,
.close-btn:focus {
    color: var(--primary-color);
    text-decoration: none;
}

/* Calibration Panel Styles */
.calibration-panel {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    padding: 1.5rem;
    margin-bottom: 2rem;
    position: relative;
    transition: all var(--transition-speed);
    border-right: 2px solid var(--secondary-color);
}

.calibration-panel:before {
    content: '';
    position: absolute;
    top: -2px; left: -2px;
    width: 50px;
    height: 50px;
    border-top: 2px solid var(--secondary-color);
    border-left: 2px solid var(--secondary-color);
}

.calibration-panel:after {
    content: '';
    position: absolute;
    bottom: -2px; right: -2px;
    width: 50px;
    height: 50px;
    border-bottom: 2px solid var(--secondary-color);
    border-right: 2px solid var(--secondary-color);
}

.calibration-status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.calibration-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-top: 1rem;
}

.calibration-instructions {
    background-color: rgba(0, 170, 255, 0.1);
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.calibration-instructions h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.calibration-instructions ol {
    margin-left: 1.5rem;
    line-height: 1.6;
}

.calibration-instructions li {
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.calibration-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
}

.btn-calibration {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

.btn-calibration:hover:not(:disabled) {
    background-color: var(--secondary-color);
    color: var(--background-color);
    box-shadow: 0 0 5px var(--secondary-color), 0 0 10px var(--secondary-color);
}

.btn-calibration-start {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: var(--background-color);
}

.btn-calibration-start:hover:not(:disabled) {
    box-shadow: 0 0 5px var(--success-color), 0 0 10px var(--success-color);
}

.btn-calibration-stop {
    background-color: var(--error-color);
    border-color: var(--error-color);
    color: var(--background-color);
}

.btn-calibration-stop:hover:not(:disabled) {
    box-shadow: 0 0 5px var(--error-color), 0 0 10px var(--error-color);
}

.calibration-progress {
    grid-column: 1 / -1;
    margin-top: 1rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: rgba(0, 170, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-text {
    text-align: center;
    color: var(--text-muted-color);
    font-size: 0.9rem;
}

.calibration-results {
    grid-column: 1 / -1;
    background-color: rgba(0, 255, 170, 0.1);
    padding: 1.5rem;
    border: 1px solid var(--success-color);
    border-radius: 4px;
    margin-top: 1rem;
}

.calibration-results h3 {
    color: var(--success-color);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(0, 255, 170, 0.2);
}

.result-item:last-child {
    border-bottom: none;
}

.result-label {
    color: var(--text-muted-color);
    font-weight: 600;
}

.result-value {
    color: var(--text-color);
    font-weight: 700;
}

/* Status card for calibration */
#calibration-status-card .status-icon {
    color: var(--secondary-color);
}

#calibration-status-card.status-calibrating .status-icon {
    color: var(--record-all-color);
    animation: pulse-orange 2s infinite;
}

#calibration-status-card.status-success .status-icon {
    color: var(--success-color);
}

#calibration-status-card.status-error .status-icon {
    color: var(--error-color);
}

/* Footer */
.footer {
    margin-top: auto;
    padding: 20px 0;
    border-top: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--surface-color) 0%, rgba(26, 26, 46, 0.8) 100%);
    backdrop-filter: blur(10px);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.footer-actions {
    display: flex;
    gap: 12px;
}

.footer-actions .btn {
    padding: 8px 16px;
    font-size: 0.85rem;
}

.footer-info {
    text-align: right;
    color: var(--text-muted-color);
    font-size: 0.9rem;
}

.footer-info p {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 6px;
    justify-content: flex-end;
}

.footer-info i {
    color: var(--primary-color);
    opacity: 0.7;
}

#lastUpdate {
    color: var(--secondary-color);
    font-weight: 500;
}

/* Data Visualization Dashboard */
.data-visualization-dashboard {
    margin-bottom: 24px;
}

.visualization-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.visualization-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 24px;
    margin-top: 24px;
}

/* 交互式速度分析占据第一行的左侧（更大空间） */
.featured-chart {
    grid-column: 1;
    grid-row: 1 / 3;
}

.chart-container {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    transition: all var(--transition-speed);
    position: relative;
    overflow: hidden;
    min-height: 380px;
}

.chart-container:hover {
    border-color: var(--border-glow);
    background: rgba(0, 212, 255, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 212, 255, 0.1);
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0.7;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
}

.chart-header h3 {
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    text-shadow: var(--subtle-glow);
}

.chart-header h3 i {
    color: var(--secondary-color);
    filter: drop-shadow(0 0 4px var(--secondary-color));
}

.chart-status {
    font-size: 0.85rem;
    color: var(--text-muted-color);
    padding: 4px 8px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

.chart-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.chart-select {
    background: rgba(0, 0, 0, 0.4);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    color: var(--text-color);
    padding: 4px 8px;
    font-size: 0.85rem;
    outline: none;
    transition: all var(--transition-speed);
}

.chart-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 8px rgba(0, 212, 255, 0.3);
}

.chart-wrapper {
    position: relative;
    height: 300px;
    width: 100%;
}

.chart-wrapper canvas {
    max-width: 100%;
    max-height: 100%;
}

/* Statistics Wrapper */
.stats-wrapper {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    height: 300px;
    align-content: start;
}

.stat-item {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 16px;
    text-align: center;
    transition: all var(--transition-speed);
    position: relative;
    overflow: hidden;
}

.stat-item:hover {
    border-color: var(--border-glow);
    background: rgba(0, 212, 255, 0.1);
    transform: translateY(-2px);
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0.6;
}

.stat-label {
    font-size: 0.85rem;
    color: var(--text-muted-color);
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-color);
    text-shadow: var(--subtle-glow);
}

/* Button Styles for Visualization */
.btn-small {
    padding: 6px 12px;
    font-size: 0.85rem;
    min-width: auto;
}

/* Panel Header Enhancement */
.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
}

/* Data Filter Panel */
.data-filter-panel {
    margin-bottom: 24px;
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    transition: all var(--transition-speed);
}

.data-filter-panel:hover {
    border-color: var(--border-glow);
    background: rgba(0, 212, 255, 0.05);
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-label {
    font-size: 0.9rem;
    color: var(--primary-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-input, .filter-select {
    background: rgba(0, 0, 0, 0.4);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    color: var(--text-color);
    padding: 8px 12px;
    font-size: 0.9rem;
    outline: none;
    transition: all var(--transition-speed);
    flex: 1;
}

.filter-input:focus, .filter-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 8px rgba(0, 212, 255, 0.3);
    background: rgba(0, 0, 0, 0.6);
}

.filter-separator {
    color: var(--text-muted-color);
    font-size: 0.85rem;
    white-space: nowrap;
}

.btn-secondary {
    background: rgba(108, 117, 125, 0.2);
    border: 1px solid rgba(108, 117, 125, 0.5);
    color: var(--text-color);
}

.btn-secondary:hover {
    background: rgba(108, 117, 125, 0.4);
    border-color: rgba(108, 117, 125, 0.8);
    transform: translateY(-2px);
}

/* 球速心电图样式 */
.ecg-current-speed {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-left: auto;
    padding: 4px 8px;
    background: rgba(0, 255, 0, 0.1);
    border: 1px solid rgba(0, 255, 0, 0.3);
    border-radius: 4px;
    font-family: 'Orbitron', monospace;
}

.ecg-speed-label {
    color: #a0a0a0;
    font-size: 11px;
}

.ecg-speed-value {
    color: #00ff00;
    font-size: 14px;
    font-weight: bold;
    text-shadow: 0 0 4px rgba(0, 255, 0, 0.5);
    min-width: 40px;
    text-align: right;
}

.ecg-speed-unit {
    color: #a0a0a0;
    font-size: 11px;
}

/* 心电图画布特殊样式 */
#speedEcgChart {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(0, 255, 0, 0.2);
    border-radius: 4px;
}

/* 交互式图表样式 */
.interactive-chart-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.interactive-chart-controls .btn {
    min-width: 36px;
    height: 36px;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.camera-toggles {
    display: flex;
    gap: 15px;
    margin-left: 15px;
}

.camera-toggle {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    user-select: none;
}

.camera-toggle input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #00ffaa;
}

.toggle-label {
    font-size: 0.9em;
    font-weight: 500;
}

.toggle-label.left-camera {
    color: rgba(54, 162, 235, 1);
}

.toggle-label.right-camera {
    color: rgba(255, 99, 132, 1);
}

.interactive-chart-wrapper {
    position: relative;
}

.chart-instructions {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 0.8em;
    color: #a0a0a0;
    pointer-events: none;
    z-index: 10;
}

.instruction-item {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 4px;
}

.instruction-item:last-child {
    margin-bottom: 0;
}

.instruction-item i {
    width: 14px;
    text-align: center;
    color: #00ffaa;
}

/* 交互式图表容器特殊样式 */
.interactive-chart-wrapper canvas {
    cursor: grab;
}

.interactive-chart-wrapper canvas:active {
    cursor: grabbing;
}

/* 重点图表样式 */
.featured-chart {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.08) 0%, rgba(0, 255, 170, 0.05) 100%);
    border: 2px solid var(--primary-color);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.featured-chart::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--primary-color));
    animation: shimmer 2s ease-in-out infinite;
}

.featured-badge {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: var(--background-color);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 8px;
    box-shadow: 0 0 8px rgba(0, 212, 255, 0.4);
}

.featured-wrapper {
    height: 500px;
}

.featured-chart:hover {
    border-color: var(--secondary-color);
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
    transform: translateY(-3px);
}

/* 次要组件样式优化 */
.chart-container:not(.featured-chart) {
    min-height: 240px;
}

.chart-container:not(.featured-chart) .chart-wrapper {
    height: 200px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .interactive-chart-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .camera-toggles {
        margin-left: 0;
        gap: 10px;
    }

    .chart-instructions {
        position: static;
        margin-top: 10px;
        background: rgba(0, 0, 0, 0.5);
    }
    
    .featured-wrapper {
        height: 350px;
    }
    
    .featured-badge {
        display: block;
        margin-left: 0;
        margin-top: 4px;
        text-align: center;
        width: fit-content;
    }
}