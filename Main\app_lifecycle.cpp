#include "app_lifecycle.hpp"

// Service and Utility Headers
#include "../Services/WebServerService.hpp"
#include "../Services/CameraService.hpp"
#include "../Services/InferenceService.hpp"
#include "../Services/RecordingService.hpp"
#include "../Services/StereoReconstructionService.hpp"
#include "../Services/DataLoggingService.hpp"
#include "../Services/CalibrationService.hpp"
#include "../Utils/SharedData.hpp"
#include "../Utils/utf8_utils.hpp"

// External Library Headers
#include "NvInferPlugin.h" 

// Standard Library Headers
#include <iostream>
#include <chrono>
#include <atomic>
#include <opencv2/opencv.hpp>

// ============================= 函数声明(内部) =============================
void process_camera_loop(
    int camera_id,
    CameraService& camera_service,
    std::unique_ptr<InferenceService> inference_service,
    std::shared_ptr<SharedData> shared_data,
    std::shared_ptr<RecordingService> recording_service,
    Services::DataLoggingService* data_logging_service,
    std::atomic<bool>& shutdown_flag
);

// ======================== 初始化与生命周期管理函数 ======================

std::unique_ptr<ServiceContext> initialize_services() {
    UTF8Utils::println("--- 阶段1: 初始化服务 ---");
    auto context = std::make_unique<ServiceContext>();

    context->shared_data = std::make_shared<SharedData>();
    UTF8Utils::println("  - 共享数据... OK");

    context->camera_service = std::make_unique<CameraService>();
    if (!context->camera_service->areCamerasInitialized()) {
        UTF8Utils::println("  - 相机服务... 失败!");
        return nullptr;
    }
    UTF8Utils::println("  - 相机服务... OK");

    UTF8Utils::println("  - 初始化TensorRT插件...");
    initLibNvInferPlugins(nullptr, "");
    UTF8Utils::println("  - TensorRT插件... OK");

    std::string engine_path = "C:\\Dev\\Camera_Editor\\Deploy\\models\\new_best.engine";
    std::string classes_path = "C:\\Dev\\Camera_Editor\\Deploy\\models\\classes.txt";
    context->inference_service_prototype = std::make_unique<InferenceService>(engine_path, classes_path);
    UTF8Utils::println("  - AI推理服务原型... OK");

    context->recording_service = std::make_shared<RecordingService>("C:/Dev/Camera_Editor/Data/recordings", context->shared_data);
    UTF8Utils::println("  - 录制服务... OK");

    context->web_service = std::make_shared<WebServerService>(context->shared_data, context->recording_service);
    UTF8Utils::println("  - Web服务... OK");

    // 新增：初始化数据记录服务
    context->data_logging_service = std::make_unique<Services::DataLoggingService>("C:/Dev/Camera_Editor/Data/telemetry.db");
    UTF8Utils::println("  - 数据记录服务... OK");

    // 注册三维坐标更新回调，当有新的三维坐标时自动广播给前端
    // 使用 weak_ptr 捕获，防止循环引用，并安全地检查对象生命周期
    std::weak_ptr<WebServerService> weak_web_service = context->web_service;
    context->shared_data->setNewBallPositionsCallback([weak_web_service]() {
        if (auto shared_web_service = weak_web_service.lock()) {
            shared_web_service->broadcast3DCoordinates();
        }
    });
    UTF8Utils::println("  - 三维坐标广播回调已注册... OK");

    context->stereo_reconstruction_service = std::make_unique<StereoReconstructionService>(
        context->shared_data,
        context->data_logging_service.get() // 将日志服务指针注入
    );
    UTF8Utils::println("  - 立体视觉重建服务... OK");

    // 初始化相机标定服务
    context->calibration_service = std::make_unique<Services::CalibrationService>(
        context->shared_data,
        1440, // 图像宽度
        1080  // 图像高度
    );
    UTF8Utils::println("  - 相机标定服务... OK");

    // 将标定服务设置到 WebServerService
    context->web_service->setCalibrationService(context->calibration_service.get());
    UTF8Utils::println("  - 标定服务已连接到Web服务... OK");

    // 初始化精彩录制服务
    context->highlight_service = std::make_unique<HighlightService>(context->shared_data);
    UTF8Utils::println("  - 精彩录制服务... OK");

    // 将精彩录制服务设置到 WebServerService
    context->web_service->setHighlightService(context->highlight_service.get());
    UTF8Utils::println("  - 精彩录制服务已连接到Web服务... OK");

    // 设置标定成功后的重新加载回调
    auto stereo_service_ptr = context->stereo_reconstruction_service.get();
    context->calibration_service->setReloadCallback([stereo_service_ptr]() {
        UTF8Utils::println("🔄 标定成功，重新加载所有服务的标定数据...");

        try {
            // 重新加载立体视觉重建服务的标定数据
            if (stereo_service_ptr) {
                stereo_service_ptr->reloadCalibrationData();
            }

            UTF8Utils::println("✅ 所有服务的标定数据已重新加载");
        } catch (const std::exception& e) {
            UTF8Utils::println("❌ 重新加载标定数据时发生错误: " + std::string(e.what()));
        }
    });
    UTF8Utils::println("  - 标定重新加载回调已设置... OK");

    UTF8Utils::println("所有服务初始化完毕。");
    return context;
}

void start_background_services(ServiceContext& context) {
    UTF8Utils::println("--- 阶段2: 启动后台服务 ---");
    // 将 shared_ptr 传递给线程，以保证 WebServerService 的生命周期与线程一致
    std::thread web_thread(&WebServerService::start, context.web_service, 8080);
    web_thread.detach(); // 分离线程，使其在后台独立运行
    UTF8Utils::println("  - Web服务已在后台分离线程中启动。");

    // 启动数据记录服务的后台线程
    context.data_logging_service->start();
    UTF8Utils::println("  - 数据记录服务已在后台线程中启动。");
}

std::vector<std::thread> launch_processing_pipelines(ServiceContext& context) {
    UTF8Utils::println("--- 阶段3: 启动并行处理流水线 ---");
    std::vector<std::thread> threads;
    std::vector<int> camera_ids = {1, 2}; // 假设我们有两个摄像头

    for (int id : camera_ids) {
        UTF8Utils::println("  - 准备为相机 " + std::to_string(id) + " 启动处理线程...");
        
        auto thread_specific_inference_service = context.inference_service_prototype->clone();
        if (!thread_specific_inference_service) {
             UTF8Utils::println("  - 错误: 克隆推理服务失败，跳过相机 " + std::to_string(id));
             continue;
        }
        
        threads.emplace_back(
            process_camera_loop,
            id,
            std::ref(*context.camera_service),
            std::move(thread_specific_inference_service),
            context.shared_data,
            context.recording_service,
            context.data_logging_service.get(),
            std::ref(context.shutdown_flag)
        );
        UTF8Utils::println("  - 相机 " + std::to_string(id) + " 的处理线程已成功启动。");
    }

    // 新增：启动一个专门的线程用于三维重建
    UTF8Utils::println("  - 准备启动立体视觉重建线程...");
    threads.emplace_back([&context]() {
        UTF8Utils::println("    - 立体视觉重建线程已启动。");
        bool is_waiting_message_shown = false; // Flag to show message only once
        while (!context.shutdown_flag.load()) {
            // 在执行任何操作前，检查系统是否已启动
            if (!context.shared_data->isSystemRunning()) {
                if (!is_waiting_message_shown) {
                    UTF8Utils::println("    - [Recon Thread] 系统未运行，暂停并等待启动信号...");
                    is_waiting_message_shown = true;
                }
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                continue;
            }
            // If we are here, it means the system is running
            if (is_waiting_message_shown) {
                UTF8Utils::println("    - [Recon Thread] 检测到启动信号，恢复处理。");
                is_waiting_message_shown = false;
            }

            if (context.stereo_reconstruction_service) {
                // processLatestDetections 内部会在成功时调用 setBallPositions3D,
                // 该函数会触发已注册的回调，自动广播3D坐标，无需在这里额外操作。
                context.stereo_reconstruction_service->processLatestDetections();
            }
            // 以大约10Hz的频率运行，与AI推理的频率大致匹配
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        UTF8Utils::println("    - 立体视觉重建线程已正常退出。");
    });
    UTF8Utils::println("  - 立体视觉重建线程已成功启动。");

    return threads;
}

void wait_for_shutdown(std::vector<std::thread>& threads, ServiceContext& context) {
    UTF8Utils::println("--- 阶段4: 等待程序关闭 ---");
    UTF8Utils::println("主线程已挂起，等待所有处理线程结束。 (按 Ctrl+C 终止)");
    
    // 等待所有流水线线程完成
    for (auto& t : threads) {
        if (t.joinable()) {
            t.join();
        }
    }
    UTF8Utils::println("所有处理流水线已关闭。");

    // 停止并清理数据记录服务，确保所有数据都已写入磁盘
    if(context.data_logging_service) {
        context.data_logging_service->stop();
    }
}


// ============================= 核心处理函数 =============================
void process_camera_loop(
    int camera_id,
    CameraService& camera_service,
    std::unique_ptr<InferenceService> inference_service,
    std::shared_ptr<SharedData> shared_data,
    std::shared_ptr<RecordingService> recording_service,
    Services::DataLoggingService* data_logging_service,
    std::atomic<bool>& shutdown_flag
) {
    UTF8Utils::println("相机处理线程 " + std::to_string(camera_id) + " 已启动，使用专属推理服务实例。");

    auto last_fps_update_time = std::chrono::steady_clock::now();
    int frame_counter = 0;
    long long main_loop_frame_counter = 0;
    const int AI_FRAME_INTERVAL = 4; // AI以1/4的帧率运行

    bool is_waiting_message_shown = false; // Flag to show message only once

    while (!shutdown_flag.load()) {
        // 在循环开始时检查系统是否应该运行
        if (!shared_data->isSystemRunning()) {
            if (!is_waiting_message_shown) {
                UTF8Utils::println("  - [Cam " + std::to_string(camera_id) + " Thread] 系统未运行，暂停并等待启动信号...");
                is_waiting_message_shown = true;
            }
            // 如果系统未启动，则线程暂停，等待启动信号
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            continue;
        }
        // If we are here, it means the system is running
        if (is_waiting_message_shown) {
            UTF8Utils::println("  - [Cam " + std::to_string(camera_id) + " Thread] 检测到启动信号，恢复处理。");
            is_waiting_message_shown = false;
        }

        cv::Mat frame = camera_service.getFrame(camera_id);
        if (!frame.empty()) {
            main_loop_frame_counter++;

            // --- 高频路径 (每一帧都执行) ---
            // 通过cv::Mat的浅拷贝分发，无数据复制
            recording_service->pushFrame(camera_id, frame);
            shared_data->setNewFrame(camera_id, frame);

            // 统计帧处理数量
            shared_data->incrementFrameCount();

            // --- 新增：独立的数据记录路径 (仅摄像头1记录，避免重复) ---
            // 记录基础帧数据，不依赖AI推理结果
            if (data_logging_service && camera_id == 1) {
                Services::DataPoint dp;
                dp.timestamp_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::system_clock::now().time_since_epoch()).count();
                dp.camera_id = camera_id;

                // 使用批量数据获取减少锁竞争
                auto data_snapshot = shared_data->getDataSnapshot();

                if (data_snapshot.has_data && !data_snapshot.ball_positions.empty()) {
                    dp.position = data_snapshot.ball_positions[0].world_position;
                } else {
                    dp.position = {0.0f, 0.0f, 0.0f}; // 默认位置
                }
                dp.speed = data_snapshot.ball_speed;

                data_logging_service->log(dp);

                // 统计数据记录数量
                shared_data->incrementDataPointCount();
            }

            // --- 低频路径 (抽帧执行，避免阻塞) ---
            if (main_loop_frame_counter % AI_FRAME_INTERVAL == 0) {
                float current_threshold = shared_data->getConfidenceThreshold();
                auto inference_result = inference_service->processFrameWithRaw(frame, current_threshold);

                // 存储转换后的检测结果（用于可视化）
                shared_data->setDetectionResult(camera_id, inference_result.detection_result);

                // 存储原始YOLO检测结果（用于三维重建）
                shared_data->setRawYoloDetections(camera_id, inference_result.raw_yolo_detections);

                // 统计AI推理次数
                shared_data->incrementAIInferenceCount();
            }

            // 更新实际帧率和数据流监控
            frame_counter++;
            auto now = std::chrono::steady_clock::now();
            auto elapsed_seconds = std::chrono::duration<double>(now - last_fps_update_time).count();
            if (elapsed_seconds >= 2.0) { // 每2秒更新一次FPS和统计信息
                double current_fps = static_cast<double>(frame_counter) / elapsed_seconds;
                shared_data->setActualFps(camera_id, current_fps);

                // 输出数据流监控统计（仅相机1输出，避免重复）
                if (camera_id == 1) {
                    auto stats = shared_data->getDataFlowStats();
                    std::string stats_msg = "📊 数据流监控 - 帧处理: " +
                        std::to_string(stats.getFrameProcessingRate()) + " FPS, 数据记录: " +
                        std::to_string(stats.getDataLoggingRate()) + " 条/秒, AI推理: " +
                        std::to_string(stats.ai_inference_count) + " 次, 3D重建: " +
                        std::to_string(stats.successful_3d_reconstructions) + " 次";
                    UTF8Utils::println(stats_msg);
                }

                last_fps_update_time = now;
                frame_counter = 0;
            }

        } else {
            // 如果相机暂时没有画面，短暂休眠，避免CPU空转
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
        // 已移除: 此处原有的5毫秒延时是主要的性能瓶颈之一
    }
    UTF8Utils::println("相机处理线程 " + std::to_string(camera_id) + " 已正常退出。");
} 