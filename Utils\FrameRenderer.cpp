#include "FrameRenderer.hpp"
#include "utf8_utils.hpp"

FrameRenderer::<PERSON>ame<PERSON><PERSON><PERSON>(std::shared_ptr<SharedData> shared_data) 
    : shared_data_(shared_data),
      box_color_(0, 255, 0),        // 绿色检测框
      text_color_(0, 0, 0),         // 黑色文本
      box_thickness_(2),            // 2像素线宽
      font_scale_(0.5),             // 字体缩放
      font_face_(cv::FONT_HERSHEY_SIMPLEX) {
}

cv::Mat Frame<PERSON>enderer::renderDetections(const cv::Mat& frame, int camera_id, bool enable_detection_boxes) {
    return renderDetectionsInternal(frame, camera_id, enable_detection_boxes);
}

cv::Mat FrameRenderer::renderDetectionsStatic(const cv::Mat& frame, int camera_id, 
                                             std::shared_ptr<SharedData> shared_data,
                                             bool enable_detection_boxes) {
    if (!shared_data || frame.empty()) {
        return frame.clone();
    }

    cv::Mat frame_to_render = frame.clone();

    if (!enable_detection_boxes) {
        return frame_to_render;
    }

    // 根据摄像头ID获取对应的检测结果
    auto result_opt = shared_data->getLatestDetectionResult(camera_id);
    
    // 如果该摄像头有检测结果，则绘制出来
    if (result_opt) {
        const auto& result = *result_opt;
        
        // 默认样式设置
        cv::Scalar box_color(0, 255, 0);    // 绿色检测框
        cv::Scalar text_color(0, 0, 0);     // 黑色文本
        int box_thickness = 2;              // 2像素线宽
        double font_scale = 0.5;            // 字体缩放
        int font_face = cv::FONT_HERSHEY_SIMPLEX;

        for (size_t i = 0; i < result.boxes.size(); ++i) {
            try {
                // 安全检查：确保索引在所有相关向量的边界内
                if (i >= result.confidences.size() || i >= result.class_names.size()) {
                    UTF8Utils::println("Error: Inconsistent detection data for camera " + 
                                      std::to_string(camera_id) + ". Skipping box " + 
                                      std::to_string(i) + ".");
                    continue; // 如果数据不一致，则跳过此检测框的绘制
                }

                // 绘制检测框
                cv::rectangle(frame_to_render, result.boxes[i], box_color, box_thickness);
                
                // 安全检查：确保类名非空
                if (result.class_names[i].empty()) {
                    continue;
                }

                // 准备标签文本
                std::string confidence_str = std::to_string(result.confidences[i]);
                if (confidence_str.length() > 4) {
                    confidence_str = confidence_str.substr(0, 4);
                }
                std::string label = result.class_names[i] + " " + confidence_str;
                
                // 绘制标签背景
                int baseline;
                cv::Size label_size = cv::getTextSize(label, font_face, font_scale, 1, &baseline);
                
                // 计算标签背景矩形
                cv::Point label_bg_top_left(result.boxes[i].x, 
                                           result.boxes[i].y - label_size.height - 5);
                cv::Point label_bg_bottom_right(result.boxes[i].x + label_size.width, 
                                               result.boxes[i].y);
                
                // 绘制标签背景
                cv::rectangle(frame_to_render, 
                            label_bg_top_left,
                            label_bg_bottom_right,
                            box_color,
                            cv::FILLED);

                // 绘制标签文本
                cv::putText(frame_to_render, label, 
                           cv::Point(result.boxes[i].x, result.boxes[i].y - 5), 
                           font_face, font_scale, text_color, 1);
                           
            } catch (const std::exception& e) {
                UTF8Utils::println("!!! CRITICAL: Exception caught while drawing box " + 
                                  std::to_string(i) + " for camera " + std::to_string(camera_id) + 
                                  ". Details: " + e.what());
            } catch (...) {
                UTF8Utils::println("!!! CRITICAL: Unknown exception caught while drawing box " + 
                                  std::to_string(i) + " for camera " + std::to_string(camera_id) + ".");
            }
        }
    }

    return frame_to_render;
}

cv::Mat FrameRenderer::renderDetectionsInternal(const cv::Mat& frame, int camera_id, bool enable_detection_boxes) {
    if (!shared_data_ || frame.empty()) {
        return frame.clone();
    }

    cv::Mat frame_to_render = frame.clone();

    if (!enable_detection_boxes) {
        return frame_to_render;
    }

    // 根据摄像头ID获取对应的检测结果
    auto result_opt = shared_data_->getLatestDetectionResult(camera_id);
    
    // 如果该摄像头有检测结果，则绘制出来
    if (result_opt) {
        const auto& result = *result_opt;
        for (size_t i = 0; i < result.boxes.size(); ++i) {
            try {
                // 安全检查：确保索引在所有相关向量的边界内
                if (i >= result.confidences.size() || i >= result.class_names.size()) {
                    UTF8Utils::println("Error: Inconsistent detection data for camera " + 
                                      std::to_string(camera_id) + ". Skipping box " + 
                                      std::to_string(i) + ".");
                    continue; // 如果数据不一致，则跳过此检测框的绘制
                }

                // 绘制检测框
                cv::rectangle(frame_to_render, result.boxes[i], box_color_, box_thickness_);
                
                // 安全检查：确保类名非空
                if (result.class_names[i].empty()) {
                    continue;
                }

                // 准备标签文本
                std::string confidence_str = std::to_string(result.confidences[i]);
                if (confidence_str.length() > 4) {
                    confidence_str = confidence_str.substr(0, 4);
                }
                std::string label = result.class_names[i] + " " + confidence_str;
                
                // 绘制标签背景
                int baseline;
                cv::Size label_size = cv::getTextSize(label, font_face_, font_scale_, 1, &baseline);
                
                // 计算标签背景矩形
                cv::Point label_bg_top_left(result.boxes[i].x, 
                                           result.boxes[i].y - label_size.height - 5);
                cv::Point label_bg_bottom_right(result.boxes[i].x + label_size.width, 
                                               result.boxes[i].y);
                
                // 绘制标签背景
                cv::rectangle(frame_to_render, 
                            label_bg_top_left,
                            label_bg_bottom_right,
                            box_color_,
                            cv::FILLED);

                // 绘制标签文本
                cv::putText(frame_to_render, label, 
                           cv::Point(result.boxes[i].x, result.boxes[i].y - 5), 
                           font_face_, font_scale_, text_color_, 1);
                           
            } catch (const std::exception& e) {
                UTF8Utils::println("!!! CRITICAL: Exception caught while drawing box " + 
                                  std::to_string(i) + " for camera " + std::to_string(camera_id) + 
                                  ". Details: " + e.what());
            } catch (...) {
                UTF8Utils::println("!!! CRITICAL: Unknown exception caught while drawing box " + 
                                  std::to_string(i) + " for camera " + std::to_string(camera_id) + ".");
            }
        }
    }

    return frame_to_render;
}

void FrameRenderer::setBoxColor(const cv::Scalar& color) {
    box_color_ = color;
}

void FrameRenderer::setTextColor(const cv::Scalar& color) {
    text_color_ = color;
}

void FrameRenderer::setBoxThickness(int thickness) {
    box_thickness_ = thickness;
}